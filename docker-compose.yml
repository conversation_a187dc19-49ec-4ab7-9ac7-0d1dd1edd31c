version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: synapseai-postgres
    environment:
      POSTGRES_DB: synapseai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./apps/backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: synapseai-redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile
    container_name: synapseai-backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/synapseai
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-here-32-chars-min
      - JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-32-chars-min
      - FRONTEND_URL=http://localhost:3000
    ports:
      - '3001:3001'
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./apps/backend:/app/apps/backend
      - ./packages:/app/packages
      - ./package.json:/app/package.json
      - ./turbo.json:/app/turbo.json
      - ./tsconfig.json:/app/tsconfig.json
      - /app/node_modules
    command: npm run dev:backend

  frontend:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile
    container_name: synapseai-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_WS_URL=ws://localhost:3001
    ports:
      - '3000:3000'
    depends_on:
      - backend
    volumes:
      - ./apps/frontend:/app/apps/frontend
      - ./packages:/app/packages
      - ./package.json:/app/package.json
      - ./turbo.json:/app/turbo.json
      - ./tsconfig.json:/app/tsconfig.json
      - /app/node_modules
      - /app/apps/frontend/.next
    command: npm run dev:frontend

volumes:
  postgres_data:
  redis_data:
