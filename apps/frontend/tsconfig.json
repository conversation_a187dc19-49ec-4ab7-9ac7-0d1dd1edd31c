{"extends": "../../tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/app/*": ["./src/app/*"], "@synapseai/types": ["../../packages/types/src"], "@synapseai/sdk": ["../../packages/sdk/src"], "@synapseai/config": ["../../packages/config/src"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}