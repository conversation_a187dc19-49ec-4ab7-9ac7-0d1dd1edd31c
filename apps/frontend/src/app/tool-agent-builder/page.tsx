"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Save, Play } from "lucide-react";
import FlowEditor from "@/components/tool-agent/FlowEditor";
import ConfigPanel from "@/components/tool-agent/ConfigPanel";
import TestConsole from "@/components/tool-agent/TestConsole";
import ComponentLibrary from "@/components/tool-agent/ComponentLibrary";

export default function ToolAgentBuilderPage() {
  const [selectedNode, setSelectedNode] = useState(null);
  const [activeTab, setActiveTab] = useState("design");
  const [flowName, setFlowName] = useState("New Tool-Agent Flow");

  // Handle node selection in the flow editor
  const handleNodeSelect = (node) => {
    setSelectedNode(node);
  };

  // Handle saving the flow
  const handleSaveFlow = () => {
    // Placeholder for save functionality
    console.log("Saving flow...");
  };

  // Handle testing the flow
  const handleTestFlow = () => {
    setActiveTab("test");
  };

  return (
    <div className="flex flex-col h-screen bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold">Tool-Agent Builder</h1>
          <input
            type="text"
            value={flowName}
            onChange={(e) => setFlowName(e.target.value)}
            className="px-2 py-1 text-lg font-medium border-none focus:outline-none bg-transparent"
          />
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleSaveFlow}>
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          <Button onClick={handleTestFlow}>
            <Play className="w-4 h-4 mr-2" />
            Test Flow
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex-1 flex flex-col"
      >
        <div className="border-b px-4">
          <TabsList>
            <TabsTrigger value="design">Design</TabsTrigger>
            <TabsTrigger value="test">Test</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent
          value="design"
          className="flex-1 flex overflow-hidden p-0 m-0"
        >
          <div className="flex flex-1 overflow-hidden">
            {/* Component Library */}
            <div className="w-64 border-r overflow-y-auto">
              <ComponentLibrary />
            </div>

            {/* Flow Editor */}
            <div className="flex-1 overflow-hidden">
              <FlowEditor onNodeSelect={handleNodeSelect} />
            </div>

            {/* Config Panel */}
            <div className="w-80 border-l overflow-y-auto">
              <ConfigPanel selectedNode={selectedNode} />
            </div>
          </div>
        </TabsContent>

        <TabsContent
          value="test"
          className="flex-1 flex flex-col overflow-hidden p-0 m-0"
        >
          <div className="flex-1 overflow-hidden p-4">
            <Card className="h-full">
              <CardContent className="p-0 h-full">
                <TestConsole flowName={flowName} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
