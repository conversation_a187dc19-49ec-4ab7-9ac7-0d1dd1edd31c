"use client";

import React, { useState } from "react";
import {
  Search,
  ChevronDown,
  ChevronRight,
  Settings,
  Code,
  Brain,
  Database,
  FileText,
  Zap,
  Workflow,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ComponentItem {
  id: string;
  name: string;
  description: string;
  type: "agent" | "tool" | "logic";
  icon: React.ReactNode;
}

interface ComponentLibraryProps {
  onDragComponent?: (component: ComponentItem) => void;
}

const ComponentLibrary = ({
  onDragComponent = () => {},
}: ComponentLibraryProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedCategories, setExpandedCategories] = useState<string[]>([
    "agents",
    "tools",
    "logic",
  ]);

  // Sample component data
  const components: ComponentItem[] = [
    // Agents
    {
      id: "agent-chat",
      name: "Chat Agent",
      description: "General purpose conversational agent",
      type: "agent",
      icon: <Brain className="h-4 w-4" />,
    },
    {
      id: "agent-task",
      name: "Task Agent",
      description: "Goal-oriented agent for completing specific tasks",
      type: "agent",
      icon: <Brain className="h-4 w-4" />,
    },
    {
      id: "agent-reasoning",
      name: "Reasoning Agent",
      description: "Agent with enhanced reasoning capabilities",
      type: "agent",
      icon: <Brain className="h-4 w-4" />,
    },

    // Tools
    {
      id: "tool-search",
      name: "Search Tool",
      description: "Search external data sources",
      type: "tool",
      icon: <Search className="h-4 w-4" />,
    },
    {
      id: "tool-database",
      name: "Database Tool",
      description: "Query and manipulate database records",
      type: "tool",
      icon: <Database className="h-4 w-4" />,
    },
    {
      id: "tool-document",
      name: "Document Tool",
      description: "Process and analyze documents",
      type: "tool",
      icon: <FileText className="h-4 w-4" />,
    },
    {
      id: "tool-code",
      name: "Code Tool",
      description: "Generate and execute code",
      type: "tool",
      icon: <Code className="h-4 w-4" />,
    },

    // Logic
    {
      id: "logic-condition",
      name: "Condition",
      description: "Branch flow based on conditions",
      type: "logic",
      icon: <Workflow className="h-4 w-4" />,
    },
    {
      id: "logic-loop",
      name: "Loop",
      description: "Repeat actions until condition is met",
      type: "logic",
      icon: <Workflow className="h-4 w-4" />,
    },
    {
      id: "logic-transform",
      name: "Transform",
      description: "Transform data between components",
      type: "logic",
      icon: <Zap className="h-4 w-4" />,
    },
  ];

  const toggleCategory = (category: string) => {
    if (expandedCategories.includes(category)) {
      setExpandedCategories(expandedCategories.filter((c) => c !== category));
    } else {
      setExpandedCategories([...expandedCategories, category]);
    }
  };

  const filteredComponents = components.filter(
    (component) =>
      component.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      component.description.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const agentComponents = filteredComponents.filter((c) => c.type === "agent");
  const toolComponents = filteredComponents.filter((c) => c.type === "tool");
  const logicComponents = filteredComponents.filter((c) => c.type === "logic");

  const handleDragStart = (e: React.DragEvent, component: ComponentItem) => {
    e.dataTransfer.setData("component", JSON.stringify(component));
    onDragComponent(component);
  };

  return (
    <div className="w-full h-full flex flex-col bg-background border rounded-md overflow-hidden">
      <div className="p-4 border-b">
        <h3 className="text-lg font-semibold mb-2">Component Library</h3>
        <Input
          placeholder="Search components..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full"
        />
      </div>

      <ScrollArea className="flex-1 p-2">
        <Accordion
          type="multiple"
          defaultValue={["agents", "tools", "logic"]}
          className="w-full"
        >
          {/* Agents Section */}
          <AccordionItem value="agents">
            <AccordionTrigger className="px-2 py-1 text-sm font-medium">
              <div className="flex items-center">
                <Brain className="h-4 w-4 mr-2" />
                Agents
                <span className="ml-2 text-xs bg-muted px-1.5 rounded-full">
                  {agentComponents.length}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-1 pl-2">
                {agentComponents.map((component) => (
                  <ComponentCard
                    key={component.id}
                    component={component}
                    onDragStart={handleDragStart}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Tools Section */}
          <AccordionItem value="tools">
            <AccordionTrigger className="px-2 py-1 text-sm font-medium">
              <div className="flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Tools
                <span className="ml-2 text-xs bg-muted px-1.5 rounded-full">
                  {toolComponents.length}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-1 pl-2">
                {toolComponents.map((component) => (
                  <ComponentCard
                    key={component.id}
                    component={component}
                    onDragStart={handleDragStart}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Logic Section */}
          <AccordionItem value="logic">
            <AccordionTrigger className="px-2 py-1 text-sm font-medium">
              <div className="flex items-center">
                <Workflow className="h-4 w-4 mr-2" />
                Logic
                <span className="ml-2 text-xs bg-muted px-1.5 rounded-full">
                  {logicComponents.length}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-1 pl-2">
                {logicComponents.map((component) => (
                  <ComponentCard
                    key={component.id}
                    component={component}
                    onDragStart={handleDragStart}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </ScrollArea>
    </div>
  );
};

interface ComponentCardProps {
  component: ComponentItem;
  onDragStart: (e: React.DragEvent, component: ComponentItem) => void;
}

const ComponentCard = ({ component, onDragStart }: ComponentCardProps) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className="flex items-center p-2 rounded-md hover:bg-accent cursor-grab text-sm"
            draggable
            onDragStart={(e) => onDragStart(e, component)}
          >
            <div className="mr-2 text-muted-foreground">{component.icon}</div>
            <div className="flex-1 truncate">{component.name}</div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="right">
          <div>
            <p className="font-medium">{component.name}</p>
            <p className="text-xs text-muted-foreground">
              {component.description}
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ComponentLibrary;
