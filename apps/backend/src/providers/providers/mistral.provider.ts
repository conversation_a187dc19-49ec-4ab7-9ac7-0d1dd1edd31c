import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import {
  BaseProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatMessage,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelConfig,
  ProviderCapabilities,
  ProviderError,
  ProviderHealth,
  ProviderMetrics,
  StreamingResponse,
} from '../interfaces/provider.interface';

interface MistralMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  name?: string;
  tool_calls?: {
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }[];
}

interface MistralTool {
  type: 'function';
  function: {
    name: string;
    description?: string;
    parameters: Record<string, any>;
  };
}

@Injectable()
export class MistralProvider extends BaseProvider {
  private readonly logger = new Logger(MistralProvider.name);
  private client!: AxiosInstance;
  private apiKey!: string;
  private baseURL: string = 'https://api.mistral.ai';
  private apiVersion: string = 'v1';
  
  private metrics: ProviderMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    totalTokensUsed: 0,
    totalCost: 0,
    rateLimit: {
      limit: 120,
      remaining: 120,
      resetTime: new Date(Date.now() + 60000),
    },
  };

  readonly name = 'mistral';
  readonly capabilities: ProviderCapabilities = {
    streaming: true,
    functionCalling: true,
    multimodal: false,
    embedding: true,
    imageGeneration: false,
    codeExecution: false,
    webSearch: false,
    maxContextLength: 32768,
    supportedFormats: ['text', 'json'],
  };

  readonly supportedModels = [
    'mistral-large-latest',
    'mistral-medium-latest',
    'mistral-small-latest',
    'open-mistral-7b',
    'open-mixtral-8x7b',
    'mistral-embed',
  ];

  readonly defaultModel = 'mistral-medium-latest';

  // Specialized task models
  readonly specializedModels = {
    code: 'mistral-large-latest',
    reasoning: 'mistral-large-latest',
    math: 'mistral-large-latest',
    writing: 'mistral-medium-latest',
    summarization: 'mistral-medium-latest',
    extraction: 'mistral-medium-latest',
    classification: 'mistral-small-latest',
  };

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeMetrics();
  }

  async initialize(config: Record<string, any>): Promise<void> {
    this.apiKey = config.apiKey || this.configService.get('MISTRAL_API_KEY');
    const baseURL = config.baseURL || this.configService.get('MISTRAL_BASE_URL');
    
    if (!this.apiKey) {
      throw new Error('Mistral API key is required');
    }

    if (baseURL) {
      this.baseURL = baseURL;
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      timeout: 60000,
    });

    this.logger.log('Mistral provider initialized successfully');
  }

  async healthCheck(): Promise<ProviderHealth> {
    const startTime = Date.now();
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const issues: string[] = [];

    try {
      // Simple health check with minimal token usage
      await this.client.post(`/${this.apiVersion}/chat/completions`, {
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 1,
        temperature: 0,
      });
    } catch (error: unknown) {
      status = 'unhealthy';
      issues.push((error as Error).message || 'Unknown error');
    }

    const latency = Date.now() - startTime;
    if (latency > 5000) {
      status = status === 'healthy' ? 'degraded' : status;
      issues.push('High latency detected');
    }

    return {
      status,
      latency,
      uptime: 99.9,
      errorRate: this.calculateErrorRate(),
      lastChecked: new Date(),
      issues,
    };
  }

  async getMetrics(): Promise<ProviderMetrics> {
    return { ...this.metrics };
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Detect specialized task and select appropriate model if not specified
      const modelId = this.selectModelForTask(request) || request.model || this.defaultModel;
      
      const formattedMessages = this.formatMessages(request.messages);
      
      const mistralRequest: any = {
        model: modelId,
        messages: formattedMessages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
      };

      // Handle function calling
      if (request.functions?.length || request.tools?.length) {
        mistralRequest.tools = this.formatTools(request.functions || request.tools || []);
      }

      // Add safe_prompt if specified in metadata
      if (request.metadata?.safe_prompt === true) {
        mistralRequest.safe_prompt = true;
      }

      // Add response format if specified in metadata
      if (request.metadata?.response_format) {
        mistralRequest.response_format = request.metadata.response_format;
      }

      const response = await this.client.post(`/${this.apiVersion}/chat/completions`, mistralRequest);
      const data = response.data;

      // Extract rate limit information if available
      if (response.headers['x-ratelimit-limit']) {
        this.metrics.rateLimit.limit = parseInt(response.headers['x-ratelimit-limit'], 10);
      }
      if (response.headers['x-ratelimit-remaining']) {
        this.metrics.rateLimit.remaining = parseInt(response.headers['x-ratelimit-remaining'], 10);
      }
      if (response.headers['x-ratelimit-reset']) {
        this.metrics.rateLimit.resetTime = new Date(parseInt(response.headers['x-ratelimit-reset'], 10) * 1000);
      }

      const formattedResponse = this.parseResponse(data);
      
      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += formattedResponse.usage.totalTokens;
      this.metrics.totalCost += this.estimateCost(
        formattedResponse.usage.totalTokens,
        modelId,
      );
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return formattedResponse;
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async *streamChatCompletion(request: ChatCompletionRequest): AsyncIterable<StreamingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Detect specialized task and select appropriate model if not specified
      const modelId = this.selectModelForTask(request) || request.model || this.defaultModel;
      
      const formattedMessages = this.formatMessages(request.messages);
      
      const mistralRequest: any = {
        model: modelId,
        messages: formattedMessages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        stream: true,
      };

      // Handle function calling
      if (request.functions?.length || request.tools?.length) {
        mistralRequest.tools = this.formatTools(request.functions || request.tools || []);
      }

      // Add safe_prompt if specified in metadata
      if (request.metadata?.safe_prompt === true) {
        mistralRequest.safe_prompt = true;
      }

      // Add response format if specified in metadata
      if (request.metadata?.response_format) {
        mistralRequest.response_format = request.metadata.response_format;
      }

      const response = await this.client.post(`/${this.apiVersion}/chat/completions`, mistralRequest, {
        responseType: 'stream',
      });

      const stream = response.data;
      let buffer = '';
      let totalPromptTokens = 0;
      let totalCompletionTokens = 0;
      
      for await (const chunk of stream) {
        buffer += chunk.toString();
        
        // Process complete events
        while (buffer.includes('\n')) {
          const newlineIndex = buffer.indexOf('\n');
          const line = buffer.substring(0, newlineIndex).trim();
          buffer = buffer.substring(newlineIndex + 1);
          
          if (!line || line === 'data: [DONE]') continue;
          
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));
              
              if (data.choices && data.choices.length > 0) {
                const choice = data.choices[0];
                
                // Track token usage if available
                if (data.usage) {
                  if (data.usage.prompt_tokens) {
                    totalPromptTokens = data.usage.prompt_tokens;
                  }
                  if (data.usage.completion_tokens) {
                    totalCompletionTokens = data.usage.completion_tokens;
                  }
                }
                
                yield {
                  id: data.id,
                  object: 'chat.completion.chunk',
                  created: data.created,
                  model: data.model,
                  choices: [
                    {
                      index: choice.index,
                      delta: {
                        role: choice.delta.role,
                        content: choice.delta.content,
                        functionCall: choice.delta.function_call,
                        toolCalls: choice.delta.tool_calls,
                      },
                      finishReason: choice.finish_reason,
                    },
                  ],
                };
              }
            } catch (e) {
              // Ignore parsing errors for incomplete JSON
            }
          }
        }
      }

      this.metrics.successfulRequests++;
      const totalTokens = totalPromptTokens + totalCompletionTokens;
      this.metrics.totalTokensUsed += totalTokens;
      this.metrics.totalCost += this.estimateCost(totalTokens, modelId);
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const modelId = request.model || 'mistral-embed';
      const inputs = Array.isArray(request.input) ? request.input : [request.input];
      
      const response = await this.client.post(`/${this.apiVersion}/embeddings`, {
        model: modelId,
        input: inputs,
        encoding_format: 'float',
      });
      
      const data = response.data;
      
      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += data.usage.total_tokens;
      this.metrics.totalCost += this.estimateCost(data.usage.total_tokens, modelId);
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return {
        object: data.object,
        data: data.data,
        model: data.model,
        usage: {
          promptTokens: data.usage.prompt_tokens,
          totalTokens: data.usage.total_tokens,
        },
      };
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  validateConfig(config: ModelConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.model || !this.supportedModels.includes(config.model)) {
      errors.push(`Unsupported model: ${config.model}`);
    }

    if (config.temperature < 0 || config.temperature > 1) {
      errors.push('Temperature must be between 0 and 1');
    }

    if (config.maxTokens < 1 || config.maxTokens > 32768) {
      errors.push('Max tokens must be between 1 and 32768');
    }

    if (config.topP && (config.topP < 0 || config.topP > 1)) {
      errors.push('Top P must be between 0 and 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  estimateCost(tokens: number, model: string): number {
    // Mistral pricing (per 1M tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'mistral-large-latest': { input: 8, output: 24 },
      'mistral-medium-latest': { input: 2.7, output: 8.1 },
      'mistral-small-latest': { input: 0.7, output: 2.1 },
      'open-mistral-7b': { input: 0.25, output: 0.25 },
      'open-mixtral-8x7b': { input: 0.7, output: 0.7 },
      'mistral-embed': { input: 0.1, output: 0 },
    };

    const modelPricing = pricing[model] || pricing[this.defaultModel];
    
    // For embedding models, all tokens are input tokens
    if (model === 'mistral-embed') {
      return (tokens * modelPricing.input) / 1000000;
    }
    
    // For chat models, assume 70% input, 30% output ratio if not specified
    const promptTokens = tokens * 0.7;
    const completionTokens = tokens * 0.3;
    
    // Convert from per 1M tokens to per token
    return ((promptTokens * modelPricing.input) + (completionTokens * modelPricing.output)) / 1000000;
  }

  formatMessages(messages: ChatMessage[]): MistralMessage[] {
    return messages.map(msg => {
      // Mistral doesn't support function role, convert to user
      if (msg.role === 'function') {
        return {
          role: 'user',
          content: `Function ${msg.name} returned: ${msg.content}`,
        };
      }
      
      const formattedMsg: MistralMessage = {
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      };
      
      if (msg.name) {
        formattedMsg.name = msg.name;
      }
      
      if (msg.toolCalls) {
        formattedMsg.tool_calls = msg.toolCalls;
      }
      
      return formattedMsg;
    });
  }

  parseResponse(response: any): ChatCompletionResponse {
    return {
      id: response.id,
      object: response.object,
      created: response.created,
      model: response.model,
      choices: response.choices.map((choice: any) => ({
        index: choice.index,
        message: {
          role: choice.message.role,
          content: choice.message.content,
          name: choice.message.name,
          functionCall: choice.message.function_call,
          toolCalls: choice.message.tool_calls,
        },
        finishReason: choice.finish_reason,
      })),
      usage: {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      },
    };
  }

  handleError(error: unknown): ProviderError {
    const err = error as any;
    let errorType: ProviderError['type'] = 'server_error';
    let retryable = false;
    let retryAfter: number | undefined;

    if (err.response) {
      const status = err.response.status;
      
      if (status === 429) {
        errorType = 'rate_limit';
        retryable = true;
        retryAfter = 60; // Default retry after 60 seconds
        
        // Extract retry-after header if available
        if (err.response.headers['retry-after']) {
          retryAfter = parseInt(err.response.headers['retry-after'], 10);
        }
      } else if (status === 413 || status === 400) {
        errorType = 'context_length';
        retryable = false;
      } else if (status >= 400 && status < 500) {
        errorType = 'invalid_request';
        retryable = false;
      }
    } else if (err.code === 'ECONNABORTED' || err.code === 'ETIMEDOUT') {
      errorType = 'timeout';
      retryable = true;
    }

    return {
      code: err.code || 'UNKNOWN',
      message: err.message || 'An unknown error occurred',
      type: errorType,
      retryable,
      retryAfter,
      details: err,
    };
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      totalTokensUsed: 0,
      totalCost: 0,
      rateLimit: {
        limit: 120, // Default Mistral rate limit (requests per minute)
        remaining: 120,
        resetTime: new Date(Date.now() + 60000),
      },
    };
  }

  private calculateErrorRate(): number {
    const total = this.metrics.totalRequests;
    return total > 0 ? (this.metrics.failedRequests / total) * 100 : 0;
  }

  private updateAverageLatency(latency: number): void {
    const total = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageLatency = 
      (this.metrics.averageLatency * (total - 1) + latency) / total;
  }

  private formatTools(tools: any[]): MistralTool[] {
    return tools.map(tool => {
      // Handle OpenAI-style function definitions
      if (tool.function) {
        return {
          type: 'function',
          function: {
            name: tool.function.name,
            description: tool.function.description || '',
            parameters: tool.function.parameters || {},
          },
        };
      }
      
      // Handle direct tool definitions
      return {
        type: 'function',
        function: {
          name: tool.name,
          description: tool.description || '',
          parameters: tool.parameters || {},
        },
      };
    });
  }

  /**
   * Selects the appropriate model for a specialized task based on the request content
   */
  private selectModelForTask(request: ChatCompletionRequest): string | null {
    // If model is explicitly specified, use that
    if (request.model) {
      return null;
    }
    
    // If task is specified in metadata, use that
    if (request.metadata?.task && typeof request.metadata.task === 'string') {
      const task = request.metadata.task.toLowerCase();
      if (task in this.specializedModels) {
        return this.specializedModels[task as keyof typeof this.specializedModels];
      }
    }
    
    // Try to detect task from the content
    const lastMessage = request.messages[request.messages.length - 1];
    if (lastMessage && typeof lastMessage.content === 'string') {
      const content = lastMessage.content.toLowerCase();
      
      // Code generation or explanation
      if (
        content.includes('write code') || 
        content.includes('generate code') || 
        content.includes('implement a function') ||
        content.includes('debug this') ||
        content.includes('fix this code')
      ) {
        return this.specializedModels.code;
      }
      
      // Math problems
      if (
        content.includes('solve this equation') ||
        content.includes('calculate') ||
        content.includes('math problem') ||
        /\d+\s*[\+\-\*\/]\s*\d+/.test(content) // Simple regex for math expressions
      ) {
        return this.specializedModels.math;
      }
      
      // Reasoning tasks
      if (
        content.includes('explain why') ||
        content.includes('reason about') ||
        content.includes('analyze') ||
        content.includes('what would happen if')
      ) {
        return this.specializedModels.reasoning;
      }
      
      // Writing tasks
      if (
        content.includes('write an essay') ||
        content.includes('write a story') ||
        content.includes('compose a') ||
        content.includes('draft a')
      ) {
        return this.specializedModels.writing;
      }
      
      // Summarization
      if (
        content.includes('summarize') ||
        content.includes('tldr') ||
        content.includes('summary of')
      ) {
        return this.specializedModels.summarization;
      }
      
      // Extraction
      if (
        content.includes('extract') ||
        content.includes('find all') ||
        content.includes('identify the') ||
        content.includes('pull out')
      ) {
        return this.specializedModels.extraction;
      }
      
      // Classification
      if (
        content.includes('classify') ||
        content.includes('categorize') ||
        content.includes('is this') ||
        content.includes('which category')
      ) {
        return this.specializedModels.classification;
      }
    }
    
    // Default to null (use default model)
    return null;
  }
}
