import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import {
  BaseProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatMessage,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelConfig,
  ProviderCapabilities,
  ProviderError,
  ProviderHealth,
  ProviderMetrics,
  StreamingResponse,
} from '../interfaces/provider.interface';

interface ClaudeMessage {
  role: 'user' | 'assistant' | 'system';
  content: string | ClaudeContent[];
  name?: string;
}

interface ClaudeContent {
  type: 'text' | 'image';
  text?: string;
  source?: {
    type: 'base64';
    media_type: string;
    data: string;
  };
}

interface ClaudeTool {
  name: string;
  description: string;
  input_schema: Record<string, any>;
}

interface ClaudeToolUseBlock {
  type: 'tool_use';
  id: string;
  name: string;
  input: Record<string, any>;
}

interface ClaudeToolResultBlock {
  type: 'tool_result';
  tool_use_id: string;
  content: string;
}

@Injectable()
export class <PERSON><PERSON><PERSON>ider extends BaseProvider {
  private readonly logger = new Logger(ClaudeProvider.name);
  private client!: AxiosInstance;
  private apiKey!: string;
  private baseURL: string = 'https://api.anthropic.com';
  private apiVersion: string = '2023-06-01';
  
  private metrics: ProviderMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    totalTokensUsed: 0,
    totalCost: 0,
    rateLimit: {
      limit: 100,
      remaining: 100,
      resetTime: new Date(Date.now() + 60000),
    },
  };

  readonly name = 'claude';
  readonly capabilities: ProviderCapabilities = {
    streaming: true,
    functionCalling: true,
    multimodal: true,
    embedding: false,
    imageGeneration: false,
    codeExecution: false,
    webSearch: false,
    maxContextLength: 200000,
    supportedFormats: ['text', 'image'],
  };

  readonly supportedModels = [
    'claude-3-5-sonnet-20241022',
    'claude-3-5-haiku-20241022',
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
  ];

  readonly defaultModel = 'claude-3-5-sonnet-20241022';

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeMetrics();
  }

  async initialize(config: Record<string, any>): Promise<void> {
    this.apiKey = config.apiKey || this.configService.get('ANTHROPIC_API_KEY');
    const baseURL = config.baseURL || this.configService.get('ANTHROPIC_BASE_URL');
    
    if (!this.apiKey) {
      throw new Error('Anthropic API key is required');
    }

    if (baseURL) {
      this.baseURL = baseURL;
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.apiKey,
        'anthropic-version': this.apiVersion,
      },
      timeout: 60000,
    });

    this.logger.log('Claude provider initialized successfully');
  }

  async healthCheck(): Promise<ProviderHealth> {
    const startTime = Date.now();
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const issues: string[] = [];

    try {
      // Simple health check with minimal token usage
      await this.client.post('/v1/messages', {
        model: this.defaultModel,
        max_tokens: 1,
        messages: [{ role: 'user', content: 'Hello' }],
      });
    } catch (error: unknown) {
      status = 'unhealthy';
      issues.push((error as Error).message || 'Unknown error');
    }

    const latency = Date.now() - startTime;
    if (latency > 5000) {
      status = status === 'healthy' ? 'degraded' : status;
      issues.push('High latency detected');
    }

    return {
      status,
      latency,
      uptime: 99.9,
      errorRate: this.calculateErrorRate(),
      lastChecked: new Date(),
      issues,
    };
  }

  async getMetrics(): Promise<ProviderMetrics> {
    return { ...this.metrics };
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const formattedMessages = this.formatMessages(request.messages);
      
      const claudeRequest: any = {
        model: request.model || this.defaultModel,
        messages: formattedMessages,
        max_tokens: request.maxTokens || 4096,
        temperature: request.temperature,
        top_p: request.topP,
        system: this.extractSystemMessage(request.messages),
      };

      // Handle tool usage
      if (request.tools && request.tools.length > 0) {
        claudeRequest.tools = this.formatTools(request.tools);
      }

      const response = await this.client.post('/v1/messages', claudeRequest);
      const data = response.data;

      // Extract rate limit information if available
      if (response.headers['x-ratelimit-limit']) {
        this.metrics.rateLimit.limit = parseInt(response.headers['x-ratelimit-limit'], 10);
      }
      if (response.headers['x-ratelimit-remaining']) {
        this.metrics.rateLimit.remaining = parseInt(response.headers['x-ratelimit-remaining'], 10);
      }
      if (response.headers['x-ratelimit-reset']) {
        this.metrics.rateLimit.resetTime = new Date(parseInt(response.headers['x-ratelimit-reset'], 10) * 1000);
      }

      const formattedResponse = this.parseResponse(data);
      
      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0);
      this.metrics.totalCost += this.estimateCost(
        (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0),
        request.model || this.defaultModel,
      );
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return formattedResponse;
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async *streamChatCompletion(request: ChatCompletionRequest): AsyncIterable<StreamingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const formattedMessages = this.formatMessages(request.messages);
      
      const claudeRequest: any = {
        model: request.model || this.defaultModel,
        messages: formattedMessages,
        max_tokens: request.maxTokens || 4096,
        temperature: request.temperature,
        top_p: request.topP,
        system: this.extractSystemMessage(request.messages),
        stream: true,
      };

      // Handle tool usage
      if (request.tools && request.tools.length > 0) {
        claudeRequest.tools = this.formatTools(request.tools);
      }

      const response = await this.client.post('/v1/messages', claudeRequest, {
        responseType: 'stream',
      });

      const stream = response.data;
      let buffer = '';
      let totalTokens = 0;
      let messageId = '';
      let modelName = request.model || this.defaultModel;
      
      for await (const chunk of stream) {
        buffer += chunk.toString();
        
        // Process complete events
        while (buffer.includes('\n')) {
          const newlineIndex = buffer.indexOf('\n');
          const line = buffer.substring(0, newlineIndex).trim();
          buffer = buffer.substring(newlineIndex + 1);
          
          if (line.startsWith('data: ')) {
            const data = line.substring(6);
            
            if (data === '[DONE]') {
              continue;
            }
            
            try {
              const event = JSON.parse(data);
              
              if (!messageId && event.message?.id) {
                messageId = event.message.id;
              }
              
              if (event.model) {
                modelName = event.model;
              }
              
              if (event.type === 'content_block_delta') {
                const delta = event.delta;
                const index = event.index || 0;
                
                if (delta.text) {
                  totalTokens += this.estimateTokens(delta.text);
                  
                  yield {
                    id: messageId,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model: modelName,
                    choices: [
                      {
                        index: 0,
                        delta: {
                          content: delta.text,
                        },
                        finishReason: null,
                      },
                    ],
                  };
                }
              } else if (event.type === 'message_stop') {
                yield {
                  id: messageId,
                  object: 'chat.completion.chunk',
                  created: Math.floor(Date.now() / 1000),
                  model: modelName,
                  choices: [
                    {
                      index: 0,
                      delta: {},
                      finishReason: 'stop',
                    },
                  ],
                };
              } else if (event.type === 'tool_use') {
                // Handle tool use events
                const toolUse = event.tool_use;
                
                yield {
                  id: messageId,
                  object: 'chat.completion.chunk',
                  created: Math.floor(Date.now() / 1000),
                  model: modelName,
                  choices: [
                    {
                      index: 0,
                      delta: {
                        toolCalls: [
                          {
                            id: toolUse.id,
                            type: 'function',
                            function: {
                              name: toolUse.name,
                              arguments: JSON.stringify(toolUse.input),
                            },
                          },
                        ],
                      },
                      finishReason: null,
                    },
                  ],
                };
              }
            } catch (e) {
              // Ignore parsing errors for incomplete JSON
            }
          }
        }
      }

      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += totalTokens;
      this.metrics.totalCost += this.estimateCost(totalTokens, request.model || this.defaultModel);
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    throw new Error('Claude embedding not supported');
  }

  validateConfig(config: ModelConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.model || !this.supportedModels.includes(config.model)) {
      errors.push(`Unsupported model: ${config.model}`);
    }

    if (config.temperature < 0 || config.temperature > 1) {
      errors.push('Temperature must be between 0 and 1');
    }

    if (config.maxTokens < 1 || config.maxTokens > 4096) {
      errors.push('Max tokens must be between 1 and 4096');
    }

    if (config.topP && (config.topP < 0 || config.topP > 1)) {
      errors.push('Top P must be between 0 and 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  estimateCost(tokens: number, model: string): number {
    // Claude pricing (per 1M tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'claude-3-5-sonnet-20241022': { input: 3, output: 15 },
      'claude-3-5-haiku-20241022': { input: 0.25, output: 1.25 },
      'claude-3-opus-20240229': { input: 15, output: 75 },
      'claude-3-sonnet-20240229': { input: 3, output: 15 },
      'claude-3-haiku-20240307': { input: 0.25, output: 1.25 },
    };

    const modelPricing = pricing[model] || pricing[this.defaultModel];
    // Assuming 70% input, 30% output ratio
    const inputTokens = tokens * 0.7;
    const outputTokens = tokens * 0.3;
    
    // Convert from per 1M tokens to per token
    return ((inputTokens * modelPricing.input) + (outputTokens * modelPricing.output)) / 1000000;
  }

  formatMessages(messages: ChatMessage[]): ClaudeMessage[] {
    const formattedMessages: ClaudeMessage[] = [];
    
    for (const msg of messages) {
      // Skip system messages as they're handled separately
      if (msg.role === 'system') {
        continue;
      }
      
      // Map function messages to user or assistant
      if (msg.role === 'function') {
        // Function responses are mapped to tool results in Claude
        const toolResult: ClaudeToolResultBlock = {
          type: 'tool_result',
          tool_use_id: msg.name || 'unknown_tool',
          content: msg.content,
        };
        
        // Add as a user message with tool result content
        formattedMessages.push({
          role: 'user',
          content: [
            {
              type: 'text',
              text: JSON.stringify(toolResult),
            },
          ],
        });
        continue;
      }
      
      const claudeMessage: ClaudeMessage = {
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content,
      };
      
      if (msg.name) {
        claudeMessage.name = msg.name;
      }
      
      formattedMessages.push(claudeMessage);
    }
    
    return formattedMessages;
  }

  parseResponse(response: any): ChatCompletionResponse {
    const content = this.extractContentFromResponse(response);
    const toolCalls = this.extractToolCallsFromResponse(response);
    
    return {
      id: response.id,
      object: 'chat.completion',
      created: Math.floor(new Date(response.created_at || Date.now()).getTime() / 1000),
      model: response.model,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: content,
            toolCalls: toolCalls,
          },
          finishReason: response.stop_reason || 'stop',
        },
      ],
      usage: {
        promptTokens: response.usage?.input_tokens || 0,
        completionTokens: response.usage?.output_tokens || 0,
        totalTokens: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0),
      },
    };
  }

  handleError(error: unknown): ProviderError {
    const err = error as any;
    let errorType: ProviderError['type'] = 'server_error';
    let retryable = false;
    let retryAfter: number | undefined;

    if (err.response) {
      const status = err.response.status;
      
      if (status === 429) {
        errorType = 'rate_limit';
        retryable = true;
        retryAfter = 60; // Default retry after 60 seconds
        
        // Extract retry-after header if available
        if (err.response.headers['retry-after']) {
          retryAfter = parseInt(err.response.headers['retry-after'], 10);
        }
      } else if (status === 413 || status === 400) {
        errorType = 'context_length';
        retryable = false;
      } else if (status >= 400 && status < 500) {
        errorType = 'invalid_request';
        retryable = false;
      }
    } else if (err.code === 'ECONNABORTED' || err.code === 'ETIMEDOUT') {
      errorType = 'timeout';
      retryable = true;
    }

    return {
      code: err.code || 'UNKNOWN',
      message: err.message || 'An unknown error occurred',
      type: errorType,
      retryable,
      retryAfter,
      details: err,
    };
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      totalTokensUsed: 0,
      totalCost: 0,
      rateLimit: {
        limit: 100, // Default Claude rate limit
        remaining: 100,
        resetTime: new Date(Date.now() + 60000),
      },
    };
  }

  private calculateErrorRate(): number {
    const total = this.metrics.totalRequests;
    return total > 0 ? (this.metrics.failedRequests / total) * 100 : 0;
  }

  private updateAverageLatency(latency: number): void {
    const total = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageLatency = 
      (this.metrics.averageLatency * (total - 1) + latency) / total;
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for Claude
    return Math.ceil(text.length / 4);
  }

  private extractSystemMessage(messages: ChatMessage[]): string | undefined {
    const systemMessages = messages.filter(msg => msg.role === 'system');
    if (systemMessages.length === 0) {
      return undefined;
    }
    
    // Combine all system messages into one
    return systemMessages.map(msg => msg.content).join('\n\n');
  }

  private formatTools(tools: any[]): ClaudeTool[] {
    return tools.map(tool => {
      // Handle OpenAI-style function definitions
      if (tool.function) {
        return {
          name: tool.function.name,
          description: tool.function.description || '',
          input_schema: tool.function.parameters || {},
        };
      }
      
      // Handle direct tool definitions
      return {
        name: tool.name,
        description: tool.description || '',
        input_schema: tool.parameters || {},
      };
    });
  }

  private extractContentFromResponse(response: any): string {
    let content = '';
    
    if (response.content && Array.isArray(response.content)) {
      for (const block of response.content) {
        if (block.type === 'text') {
          content += block.text;
        }
      }
    } else if (typeof response.content === 'string') {
      content = response.content;
    }
    
    return content;
  }

  private extractToolCallsFromResponse(response: any): any[] {
    const toolCalls: any[] = [];
    
    if (response.content && Array.isArray(response.content)) {
      for (const block of response.content) {
        if (block.type === 'tool_use') {
          toolCalls.push({
            id: block.id,
            type: 'function',
            function: {
              name: block.name,
              arguments: JSON.stringify(block.input),
            },
          });
        }
      }
    }
    
    return toolCalls;
  }
}
