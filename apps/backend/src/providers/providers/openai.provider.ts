import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

import {
  BaseProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatMessage,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelConfig,
  ProviderCapabilities,
  ProviderError,
  ProviderHealth,
  ProviderMetrics,
  StreamingResponse,
} from '../interfaces/provider.interface';

@Injectable()
export class OpenAIProvider extends BaseProvider {
  private readonly logger = new Logger(OpenAIProvider.name);
  private client!: OpenAI;
  private metrics: ProviderMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    totalTokensUsed: 0,
    totalCost: 0,
    rateLimit: {
      limit: 3500,
      remaining: 3500,
      resetTime: new Date(Date.now() + 60000),
    },
  };

  readonly name = 'openai';
  readonly capabilities: ProviderCapabilities = {
    streaming: true,
    functionCalling: true,
    multimodal: true,
    embedding: true,
    imageGeneration: true,
    codeExecution: false,
    webSearch: false,
    maxContextLength: 128000,
    supportedFormats: ['text', 'image', 'json'],
  };

  readonly supportedModels = [
    'gpt-4o',
    'gpt-4o-mini',
    'gpt-4-turbo',
    'gpt-4',
    'gpt-3.5-turbo',
    'gpt-3.5-turbo-16k',
  ];

  readonly defaultModel = 'gpt-4o-mini';

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeMetrics();
  }

  async initialize(config: Record<string, any>): Promise<void> {
    const apiKey = config.apiKey || this.configService.get('OPENAI_API_KEY');
    const organization = config.organization || this.configService.get('OPENAI_ORGANIZATION');
    const baseURL = config.baseURL || this.configService.get('OPENAI_BASE_URL');

    if (!apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.client = new OpenAI({
      apiKey,
      organization,
      baseURL,
    });

    this.logger.log('OpenAI provider initialized successfully');
  }

  async healthCheck(): Promise<ProviderHealth> {
    const startTime = Date.now();
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const issues: string[] = [];

    try {
      // Simple health check with minimal token usage
      const response = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 1,
        temperature: 0,
      });

      if (!response.choices || response.choices.length === 0) {
        status = 'degraded';
        issues.push('No response choices returned');
      }
    } catch (error: unknown) {
      status = 'unhealthy';
      issues.push((error as Error).message || 'Unknown error');
    }

    const latency = Date.now() - startTime;
    if (latency > 5000) {
      status = status === 'healthy' ? 'degraded' : status;
      issues.push('High latency detected');
    }

    return {
      status,
      latency,
      uptime: 99.9, // Would be calculated from historical data
      errorRate: this.calculateErrorRate(),
      lastChecked: new Date(),
      issues,
    };
  }

  async getMetrics(): Promise<ProviderMetrics> {
    return { ...this.metrics };
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Enhanced function calling support
      const openaiRequest: any = {
        model: request.model,
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        user: request.userId,
      };

      // Handle function calling
      if (request.functions && request.functions.length > 0) {
        openaiRequest.functions = request.functions;
        // For older models that use function_call parameter
        if (request.model.includes('gpt-3.5') || request.model.includes('gpt-4-')) {
          openaiRequest.function_call = 'auto';
        }
      }

      // Handle tool calling (newer models)
      if (request.tools && request.tools.length > 0) {
        openaiRequest.tools = request.tools;
        openaiRequest.tool_choice = 'auto';
      }

      const response = await this.client.chat.completions.create(openaiRequest);

      const formattedResponse = this.parseResponse(response);
      
      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += formattedResponse.usage.totalTokens;
      this.metrics.totalCost += this.estimateCost(
        formattedResponse.usage.totalTokens,
        request.model,
      );
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return formattedResponse;
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async *streamChatCompletion(request: ChatCompletionRequest): AsyncIterable<StreamingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Enhanced function calling support for streaming
      const openaiRequest: any = {
        model: request.model,
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        user: request.userId,
        stream: true,
      };

      // Handle function calling
      if (request.functions && request.functions.length > 0) {
        openaiRequest.functions = request.functions;
        // For older models that use function_call parameter
        if (request.model.includes('gpt-3.5') || request.model.includes('gpt-4-')) {
          openaiRequest.function_call = 'auto';
        }
      }

      // Handle tool calling (newer models)
      if (request.tools && request.tools.length > 0) {
        openaiRequest.tools = request.tools;
        openaiRequest.tool_choice = 'auto';
      }

      const stream = await this.client.chat.completions.create(openaiRequest);

      let totalTokens = 0;
      for await (const chunk of stream) {
        if (chunk.choices && chunk.choices.length > 0) {
          const choice = chunk.choices[0];
          
          // Track tokens for content, function calls, and tool calls
          if (choice.delta) {
            if (choice.delta.content) {
              totalTokens += this.estimateTokens(choice.delta.content);
            }
            
            // Track function call tokens
            if (choice.delta.function_call?.arguments) {
              totalTokens += this.estimateTokens(choice.delta.function_call.arguments);
            }
            
            // Track tool call tokens
            if (choice.delta.tool_calls) {
              for (const toolCall of choice.delta.tool_calls) {
                if (toolCall.function?.arguments) {
                  totalTokens += this.estimateTokens(toolCall.function.arguments);
                }
              }
            }
          }

          yield {
            id: chunk.id,
            object: chunk.object,
            created: chunk.created,
            model: chunk.model,
            choices: chunk.choices.map((c: any) => ({
              index: c.index,
              delta: c.delta,
              finishReason: c.finish_reason,
            })),
          };
        }
      }

      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += totalTokens;
      this.metrics.totalCost += this.estimateCost(totalTokens, request.model);
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const response = await this.client.embeddings.create({
        model: request.model || 'text-embedding-3-small',
        input: request.input,
        dimensions: request.dimensions,
        user: request.userId,
      });

      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += response.usage.total_tokens;
      this.metrics.totalCost += this.estimateCost(
        response.usage.total_tokens,
        request.model || 'text-embedding-3-small',
      );
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return {
        object: response.object,
        data: response.data,
        model: response.model,
        usage: {
          promptTokens: response.usage.prompt_tokens,
          totalTokens: response.usage.total_tokens,
        },
      };
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  validateConfig(config: ModelConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.model || !this.supportedModels.includes(config.model)) {
      errors.push(`Unsupported model: ${config.model}`);
    }

    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('Temperature must be between 0 and 2');
    }

    if (config.maxTokens < 1 || config.maxTokens > 8192) {
      errors.push('Max tokens must be between 1 and 8192');
    }

    if (config.topP && (config.topP < 0 || config.topP > 1)) {
      errors.push('Top P must be between 0 and 1');
    }

    if (config.frequencyPenalty && (config.frequencyPenalty < -2 || config.frequencyPenalty > 2)) {
      errors.push('Frequency penalty must be between -2 and 2');
    }

    if (config.presencePenalty && (config.presencePenalty < -2 || config.presencePenalty > 2)) {
      errors.push('Presence penalty must be between -2 and 2');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  estimateCost(tokens: number, model: string): number {
    // OpenAI pricing (per 1K tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'gpt-4o': { input: 0.005, output: 0.015 },
      'gpt-4o-mini': { input: 0.00015, output: 0.0006 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-3.5-turbo': { input: 0.001, output: 0.002 },
      'text-embedding-3-small': { input: 0.00002, output: 0 },
      'text-embedding-3-large': { input: 0.00013, output: 0 },
    };

    const modelPricing = pricing[model] || pricing['gpt-3.5-turbo'];
    // Assuming 70% input, 30% output ratio
    const inputTokens = tokens * 0.7;
    const outputTokens = tokens * 0.3;
    
    return ((inputTokens * modelPricing.input) + (outputTokens * modelPricing.output)) / 1000;
  }

  formatMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => {
      const formattedMsg: any = {
        role: msg.role,
        content: msg.content,
      };

      if (msg.name) {
        formattedMsg.name = msg.name;
      }

      if (msg.functionCall) {
        formattedMsg.function_call = {
          name: msg.functionCall.name,
          arguments: msg.functionCall.arguments,
        };
      }

      if (msg.toolCalls && msg.toolCalls.length > 0) {
        formattedMsg.tool_calls = msg.toolCalls.map(toolCall => ({
          id: toolCall.id,
          type: toolCall.type,
          function: {
            name: toolCall.function.name,
            arguments: toolCall.function.arguments,
          },
        }));
      }

      return formattedMsg;
    });
  }

  parseResponse(response: any): ChatCompletionResponse {
    const parsedResponse: ChatCompletionResponse = {
      id: response.id,
      object: response.object,
      created: response.created,
      model: response.model,
      choices: response.choices.map((choice: any) => {
        const parsedChoice: any = {
          index: choice.index,
          message: {
            role: choice.message.role,
            content: choice.message.content || '',
          },
          finishReason: choice.finish_reason,
        };

        // Handle name if present
        if (choice.message.name) {
          parsedChoice.message.name = choice.message.name;
        }

        // Handle function calls
        if (choice.message.function_call) {
          parsedChoice.message.functionCall = {
            name: choice.message.function_call.name,
            arguments: choice.message.function_call.arguments,
          };
        }

        // Handle tool calls
        if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
          parsedChoice.message.toolCalls = choice.message.tool_calls.map((toolCall: any) => ({
            id: toolCall.id,
            type: toolCall.type,
            function: {
              name: toolCall.function.name,
              arguments: toolCall.function.arguments,
            },
          }));
        }

        return parsedChoice;
      }),
      usage: {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      },
    };

    return parsedResponse;
  }

  handleError(error: unknown): ProviderError {
    const err = error as any;
    let errorType: ProviderError['type'] = 'server_error';
    let retryable = false;
    let retryAfter: number | undefined;

    if (err.status === 429) {
      errorType = 'rate_limit';
      retryable = true;
      retryAfter = 60; // Default retry after 60 seconds
    } else if (err.status === 413) {
      errorType = 'context_length';
      retryable = false;
    } else if (err.status >= 400 && err.status < 500) {
      errorType = 'invalid_request';
      retryable = false;
    } else if (err.code === 'ECONNABORTED' || err.code === 'ETIMEDOUT') {
      errorType = 'timeout';
      retryable = true;
    }

    return {
      code: err.code || 'UNKNOWN',
      message: err.message || 'An unknown error occurred',
      type: errorType,
      retryable,
      retryAfter,
      details: err,
    };
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      totalTokensUsed: 0,
      totalCost: 0,
      rateLimit: {
        limit: 3500, // Default OpenAI rate limit
        remaining: 3500,
        resetTime: new Date(Date.now() + 60000),
      },
    };
  }

  private calculateErrorRate(): number {
    const total = this.metrics.totalRequests;
    return total > 0 ? (this.metrics.failedRequests / total) * 100 : 0;
  }

  private updateAverageLatency(latency: number): void {
    const total = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageLatency = 
      (this.metrics.averageLatency * (total - 1) + latency) / total;
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }
}
