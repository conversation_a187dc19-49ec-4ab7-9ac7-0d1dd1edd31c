import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import {
  BaseProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatMessage,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelConfig,
  ProviderCapabilities,
  ProviderError,
  ProviderHealth,
  ProviderMetrics,
  StreamingResponse,
} from '../interfaces/provider.interface';

@Injectable()
export class OpenRouterProvider extends BaseProvider {
  private readonly logger = new Logger(OpenRouterProvider.name);
  private client!: AxiosInstance;
  private apiKey!: string;
  private baseURL: string = 'https://openrouter.ai/api';
  private apiVersion: string = 'v1';
  
  private metrics: ProviderMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    totalTokensUsed: 0,
    totalCost: 0,
    rateLimit: {
      limit: 60,
      remaining: 60,
      resetTime: new Date(Date.now() + 60000),
    },
  };

  readonly name = 'openrouter';
  readonly capabilities: ProviderCapabilities = {
    streaming: true,
    functionCalling: true,
    multimodal: true,
    embedding: false,
    imageGeneration: false,
    codeExecution: false,
    webSearch: false,
    maxContextLength: 128000, // Varies by model
    supportedFormats: ['text', 'image', 'json'],
  };

  // A subset of popular models available through OpenRouter
  readonly supportedModels = [
    // OpenAI models
    'openai/gpt-4o',
    'openai/gpt-4-turbo',
    'openai/gpt-3.5-turbo',
    // Anthropic models
    'anthropic/claude-3-opus',
    'anthropic/claude-3-sonnet',
    'anthropic/claude-3-haiku',
    // Meta models
    'meta/llama-3-70b-instruct',
    'meta/llama-3-8b-instruct',
    // Mistral models
    'mistral/mistral-large',
    'mistral/mistral-medium',
    'mistral/mistral-small',
    // Google models
    'google/gemini-pro',
    // Other models
    'perplexity/sonar-small-online',
    'cohere/command-r',
  ];

  readonly defaultModel = 'openai/gpt-3.5-turbo';

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeMetrics();
  }

  async initialize(config: Record<string, any>): Promise<void> {
    this.apiKey = config.apiKey || this.configService.get('OPENROUTER_API_KEY');
    const baseURL = config.baseURL || this.configService.get('OPENROUTER_BASE_URL');
    
    if (!this.apiKey) {
      throw new Error('OpenRouter API key is required');
    }

    if (baseURL) {
      this.baseURL = baseURL;
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': config.referer || this.configService.get('OPENROUTER_REFERER') || 'https://api.example.com',
        'X-Title': config.title || this.configService.get('OPENROUTER_TITLE') || 'API Integration',
      },
      timeout: 60000,
    });

    this.logger.log('OpenRouter provider initialized successfully');
  }

  async healthCheck(): Promise<ProviderHealth> {
    const startTime = Date.now();
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const issues: string[] = [];

    try {
      // Simple health check with minimal token usage
      await this.client.post(`/${this.apiVersion}/chat/completions`, {
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 1,
        temperature: 0,
      });
    } catch (error: unknown) {
      status = 'unhealthy';
      issues.push((error as Error).message || 'Unknown error');
    }

    const latency = Date.now() - startTime;
    if (latency > 5000) {
      status = status === 'healthy' ? 'degraded' : status;
      issues.push('High latency detected');
    }

    return {
      status,
      latency,
      uptime: 99.9,
      errorRate: this.calculateErrorRate(),
      lastChecked: new Date(),
      issues,
    };
  }

  async getMetrics(): Promise<ProviderMetrics> {
    return { ...this.metrics };
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const modelId = request.model || this.defaultModel;
      
      // OpenRouter uses OpenAI-compatible API with some extensions
      const openRouterRequest: any = {
        model: modelId,
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
      };

      // Handle function calling
      if (request.functions?.length) {
        openRouterRequest.functions = request.functions;
        openRouterRequest.function_call = 'auto';
      }

      // Handle tool calling
      if (request.tools?.length) {
        openRouterRequest.tools = request.tools;
        openRouterRequest.tool_choice = 'auto';
      }

      // Add response format if specified in metadata
      if (request.metadata?.response_format) {
        openRouterRequest.response_format = request.metadata.response_format;
      }

      // Add OpenRouter-specific options
      if (request.metadata?.openrouter) {
        openRouterRequest.transforms = request.metadata.openrouter.transforms;
        openRouterRequest.route = request.metadata.openrouter.route;
      }

      const response = await this.client.post(`/${this.apiVersion}/chat/completions`, openRouterRequest);
      const data = response.data;

      // Extract rate limit information if available
      if (response.headers['x-ratelimit-limit']) {
        this.metrics.rateLimit.limit = parseInt(response.headers['x-ratelimit-limit'], 10);
      }
      if (response.headers['x-ratelimit-remaining']) {
        this.metrics.rateLimit.remaining = parseInt(response.headers['x-ratelimit-remaining'], 10);
      }
      if (response.headers['x-ratelimit-reset']) {
        this.metrics.rateLimit.resetTime = new Date(parseInt(response.headers['x-ratelimit-reset'], 10) * 1000);
      }

      const formattedResponse = this.parseResponse(data);
      
      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += formattedResponse.usage.totalTokens;
      this.metrics.totalCost += this.estimateCost(
        formattedResponse.usage.totalTokens,
        modelId,
      );
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return formattedResponse;
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async *streamChatCompletion(request: ChatCompletionRequest): AsyncIterable<StreamingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const modelId = request.model || this.defaultModel;
      
      // OpenRouter uses OpenAI-compatible API with some extensions
      const openRouterRequest: any = {
        model: modelId,
        messages: this.formatMessages(request.messages),
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: true,
      };

      // Handle function calling
      if (request.functions?.length) {
        openRouterRequest.functions = request.functions;
        openRouterRequest.function_call = 'auto';
      }

      // Handle tool calling
      if (request.tools?.length) {
        openRouterRequest.tools = request.tools;
        openRouterRequest.tool_choice = 'auto';
      }

      // Add response format if specified in metadata
      if (request.metadata?.response_format) {
        openRouterRequest.response_format = request.metadata.response_format;
      }

      // Add OpenRouter-specific options
      if (request.metadata?.openrouter) {
        openRouterRequest.transforms = request.metadata.openrouter.transforms;
        openRouterRequest.route = request.metadata.openrouter.route;
      }

      const response = await this.client.post(`/${this.apiVersion}/chat/completions`, openRouterRequest, {
        responseType: 'stream',
      });

      const stream = response.data;
      let buffer = '';
      let totalTokens = 0;
      
      for await (const chunk of stream) {
        buffer += chunk.toString();
        
        // Process complete events
        while (buffer.includes('\n')) {
          const newlineIndex = buffer.indexOf('\n');
          const line = buffer.substring(0, newlineIndex).trim();
          buffer = buffer.substring(newlineIndex + 1);
          
          if (!line || line === 'data: [DONE]') continue;
          
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));
              
              if (data.choices && data.choices.length > 0) {
                const choice = data.choices[0];
                
                // Estimate tokens for content
                if (choice.delta.content) {
                  totalTokens += this.estimateTokens(choice.delta.content);
                }
                
                yield {
                  id: data.id,
                  object: 'chat.completion.chunk',
                  created: data.created,
                  model: data.model,
                  choices: [
                    {
                      index: choice.index,
                      delta: {
                        role: choice.delta.role,
                        content: choice.delta.content,
                        functionCall: choice.delta.function_call,
                        toolCalls: choice.delta.tool_calls,
                      },
                      finishReason: choice.finish_reason,
                    },
                  ],
                };
              }
            } catch (e) {
              // Ignore parsing errors for incomplete JSON
            }
          }
        }
      }

      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += totalTokens;
      this.metrics.totalCost += this.estimateCost(totalTokens, modelId);
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    throw new Error('Embedding not supported by OpenRouter');
  }

  validateConfig(config: ModelConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.model || !this.supportedModels.includes(config.model)) {
      errors.push(`Unsupported model: ${config.model}`);
    }

    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('Temperature must be between 0 and 2');
    }

    if (config.maxTokens < 1) {
      errors.push('Max tokens must be at least 1');
    }

    if (config.topP && (config.topP < 0 || config.topP > 1)) {
      errors.push('Top P must be between 0 and 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  estimateCost(tokens: number, model: string): number {
    // OpenRouter pricing (per 1M tokens) - simplified version
    const pricing: Record<string, { input: number; output: number }> = {
      // OpenAI models
      'openai/gpt-4o': { input: 5, output: 15 },
      'openai/gpt-4-turbo': { input: 10, output: 30 },
      'openai/gpt-3.5-turbo': { input: 1, output: 2 },
      // Anthropic models
      'anthropic/claude-3-opus': { input: 15, output: 75 },
      'anthropic/claude-3-sonnet': { input: 3, output: 15 },
      'anthropic/claude-3-haiku': { input: 0.25, output: 1.25 },
      // Meta models
      'meta/llama-3-70b-instruct': { input: 0.9, output: 0.9 },
      'meta/llama-3-8b-instruct': { input: 0.2, output: 0.2 },
      // Mistral models
      'mistral/mistral-large': { input: 8, output: 24 },
      'mistral/mistral-medium': { input: 2.7, output: 8.1 },
      'mistral/mistral-small': { input: 0.7, output: 2.1 },
      // Google models
      'google/gemini-pro': { input: 0.125, output: 0.375 },
      // Other models
      'perplexity/sonar-small-online': { input: 0.5, output: 1.5 },
      'cohere/command-r': { input: 1, output: 3 },
    };

    // Extract provider/model from the full model ID
    const modelParts = model.split('/');
    const modelKey = modelParts.length > 1 ? model : `openai/${model}`;
    
    const modelPricing = pricing[modelKey] || pricing['openai/gpt-3.5-turbo'];
    
    // Assuming 70% input, 30% output ratio
    const inputTokens = tokens * 0.7;
    const outputTokens = tokens * 0.3;
    
    // Convert from per 1M tokens to per token
    return ((inputTokens * modelPricing.input) + (outputTokens * modelPricing.output)) / 1000000;
  }

  formatMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => {
      const formattedMsg: any = {
        role: msg.role,
        content: msg.content,
      };
      
      if (msg.name) {
        formattedMsg.name = msg.name;
      }
      
      if (msg.functionCall) {
        formattedMsg.function_call = {
          name: msg.functionCall.name,
          arguments: msg.functionCall.arguments,
        };
      }
      
      if (msg.toolCalls) {
        formattedMsg.tool_calls = msg.toolCalls.map(toolCall => ({
          id: toolCall.id,
          type: toolCall.type,
          function: {
            name: toolCall.function.name,
            arguments: toolCall.function.arguments,
          },
        }));
      }
      
      return formattedMsg;
    });
  }

  parseResponse(response: any): ChatCompletionResponse {
    return {
      id: response.id,
      object: response.object,
      created: response.created,
      model: response.model,
      choices: response.choices.map((choice: any) => {
        const parsedChoice: any = {
          index: choice.index,
          message: {
            role: choice.message.role,
            content: choice.message.content || '',
          },
          finishReason: choice.finish_reason,
        };
        
        if (choice.message.name) {
          parsedChoice.message.name = choice.message.name;
        }
        
        if (choice.message.function_call) {
          parsedChoice.message.functionCall = {
            name: choice.message.function_call.name,
            arguments: choice.message.function_call.arguments,
          };
        }
        
        if (choice.message.tool_calls) {
          parsedChoice.message.toolCalls = choice.message.tool_calls.map((toolCall: any) => ({
            id: toolCall.id,
            type: toolCall.type,
            function: {
              name: toolCall.function.name,
              arguments: toolCall.function.arguments,
            },
          }));
        }
        
        return parsedChoice;
      }),
      usage: {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      },
      metadata: response.openrouter ? { openrouter: response.openrouter } : undefined,
    };
  }

  handleError(error: unknown): ProviderError {
    const err = error as any;
    let errorType: ProviderError['type'] = 'server_error';
    let retryable = false;
    let retryAfter: number | undefined;

    if (err.response) {
      const status = err.response.status;
      
      if (status === 429) {
        errorType = 'rate_limit';
        retryable = true;
        retryAfter = 60; // Default retry after 60 seconds
        
        // Extract retry-after header if available
        if (err.response.headers['retry-after']) {
          retryAfter = parseInt(err.response.headers['retry-after'], 10);
        }
      } else if (status === 413 || status === 400) {
        errorType = 'context_length';
        retryable = false;
      } else if (status >= 400 && status < 500) {
        errorType = 'invalid_request';
        retryable = false;
      }
    } else if (err.code === 'ECONNABORTED' || err.code === 'ETIMEDOUT') {
      errorType = 'timeout';
      retryable = true;
    }

    return {
      code: err.code || 'UNKNOWN',
      message: err.message || 'An unknown error occurred',
      type: errorType,
      retryable,
      retryAfter,
      details: err,
    };
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      totalTokensUsed: 0,
      totalCost: 0,
      rateLimit: {
        limit: 60, // Default rate limit
        remaining: 60,
        resetTime: new Date(Date.now() + 60000),
      },
    };
  }

  private calculateErrorRate(): number {
    const total = this.metrics.totalRequests;
    return total > 0 ? (this.metrics.failedRequests / total) * 100 : 0;
  }

  private updateAverageLatency(latency: number): void {
    const total = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageLatency = 
      (this.metrics.averageLatency * (total - 1) + latency) / total;
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }
}
