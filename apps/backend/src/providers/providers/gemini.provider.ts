import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import {
  BaseProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatMessage,
  EmbeddingRequest,
  EmbeddingResponse,
  ModelConfig,
  ProviderCapabilities,
  ProviderError,
  ProviderHealth,
  ProviderMetrics,
  StreamingResponse,
} from '../interfaces/provider.interface';

interface GeminiPart {
  text?: string;
  inlineData?: {
    mimeType: string;
    data: string;
  };
  functionCall?: {
    name: string;
    args: Record<string, any>;
  };
  functionResponse?: {
    name: string;
    response: Record<string, any>;
  };
}

interface GeminiContent {
  role: 'user' | 'model' | 'function';
  parts: GeminiPart[];
}

interface GeminiTool {
  functionDeclarations: {
    name: string;
    description?: string;
    parameters: Record<string, any>;
  }[];
}

@Injectable()
export class GeminiProvider extends BaseProvider {
  private readonly logger = new Logger(GeminiProvider.name);
  private client!: AxiosInstance;
  private apiKey!: string;
  private baseURL: string = 'https://generativelanguage.googleapis.com';
  private apiVersion: string = 'v1';
  
  private metrics: ProviderMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    totalTokensUsed: 0,
    totalCost: 0,
    rateLimit: {
      limit: 60,
      remaining: 60,
      resetTime: new Date(Date.now() + 60000),
    },
  };

  readonly name = 'gemini';
  readonly capabilities: ProviderCapabilities = {
    streaming: true,
    functionCalling: true,
    multimodal: true,
    embedding: true,
    imageGeneration: false,
    codeExecution: true,
    webSearch: false,
    maxContextLength: 1000000,
    supportedFormats: ['text', 'image', 'video', 'audio'],
  };

  readonly supportedModels = [
    'gemini-1.5-pro',
    'gemini-1.5-flash',
    'gemini-1.0-pro',
  ];

  readonly defaultModel = 'gemini-1.5-flash';

  constructor(private readonly configService: ConfigService) {
    super();
    this.initializeMetrics();
  }

  async initialize(config: Record<string, any>): Promise<void> {
    this.apiKey = config.apiKey || this.configService.get('GOOGLE_API_KEY');
    const baseURL = config.baseURL || this.configService.get('GEMINI_BASE_URL');
    
    if (!this.apiKey) {
      throw new Error('Google API key is required');
    }

    if (baseURL) {
      this.baseURL = baseURL;
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 60000,
    });

    this.logger.log('Gemini provider initialized successfully');
  }

  async healthCheck(): Promise<ProviderHealth> {
    const startTime = Date.now();
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const issues: string[] = [];

    try {
      // Simple health check with minimal token usage
      const url = `/${this.apiVersion}/models/${this.defaultModel}:generateContent?key=${this.apiKey}`;
      await this.client.post(url, {
        contents: [
          {
            role: 'user',
            parts: [{ text: 'Hello' }],
          },
        ],
        generationConfig: {
          maxOutputTokens: 1,
          temperature: 0,
        },
      });
    } catch (error: unknown) {
      status = 'unhealthy';
      issues.push((error as Error).message || 'Unknown error');
    }

    const latency = Date.now() - startTime;
    if (latency > 5000) {
      status = status === 'healthy' ? 'degraded' : status;
      issues.push('High latency detected');
    }

    return {
      status,
      latency,
      uptime: 99.9,
      errorRate: this.calculateErrorRate(),
      lastChecked: new Date(),
      issues,
    };
  }

  async getMetrics(): Promise<ProviderMetrics> {
    return { ...this.metrics };
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const formattedMessages = this.formatMessages(request.messages);
      const modelId = request.model || this.defaultModel;
      
      const geminiRequest: any = {
        contents: formattedMessages,
        generationConfig: {
          temperature: request.temperature,
          maxOutputTokens: request.maxTokens,
          topP: request.topP,
        },
      };

      // Handle function calling
      if (request.functions?.length || request.tools?.length) {
        geminiRequest.tools = [
          {
            functionDeclarations: this.formatFunctions(request.functions || request.tools || []),
          },
        ];
      }

      const url = `/${this.apiVersion}/models/${modelId}:generateContent?key=${this.apiKey}`;
      const response = await this.client.post(url, geminiRequest);
      const data = response.data;

      // Store the model ID in the response for parseResponse to use
      data._modelId = modelId;
      
      const formattedResponse = this.parseResponse(data);
      
      this.metrics.successfulRequests++;
      
      // Gemini doesn't provide token counts directly, so we estimate
      const promptTokens = this.estimatePromptTokens(request.messages);
      const completionTokens = this.estimateCompletionTokens(formattedResponse.choices[0].message.content);
      const totalTokens = promptTokens + completionTokens;
      
      this.metrics.totalTokensUsed += totalTokens;
      this.metrics.totalCost += this.estimateCost(totalTokens, modelId);
      
      // Add token usage to the response
      formattedResponse.usage = {
        promptTokens,
        completionTokens,
        totalTokens,
      };
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return formattedResponse;
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async *streamChatCompletion(request: ChatCompletionRequest): AsyncIterable<StreamingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const formattedMessages = this.formatMessages(request.messages);
      const modelId = request.model || this.defaultModel;
      
      const geminiRequest: any = {
        contents: formattedMessages,
        generationConfig: {
          temperature: request.temperature,
          maxOutputTokens: request.maxTokens,
          topP: request.topP,
        },
        streamGenerationConfig: {
          streamContentTypes: ['text', 'function_call'],
        },
      };

      // Handle function calling
      if (request.functions?.length || request.tools?.length) {
        geminiRequest.tools = [
          {
            functionDeclarations: this.formatFunctions(request.functions || request.tools || []),
          },
        ];
      }

      const url = `/${this.apiVersion}/models/${modelId}:streamGenerateContent?key=${this.apiKey}`;
      const response = await this.client.post(url, geminiRequest, {
        responseType: 'stream',
      });

      const stream = response.data;
      let buffer = '';
      let totalTokens = 0;
      const messageId = `gemini-${Date.now()}`;
      
      for await (const chunk of stream) {
        buffer += chunk.toString();
        
        // Process complete events
        while (buffer.includes('\n')) {
          const newlineIndex = buffer.indexOf('\n');
          const line = buffer.substring(0, newlineIndex).trim();
          buffer = buffer.substring(newlineIndex + 1);
          
          if (!line) continue;
          
          try {
            const data = JSON.parse(line);
            
            if (data.candidates && data.candidates.length > 0) {
              const candidate = data.candidates[0];
              
              if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
                for (const part of candidate.content.parts) {
                  if (part.text) {
                    totalTokens += this.estimateTokens(part.text);
                    
                    yield {
                      id: messageId,
                      object: 'chat.completion.chunk',
                      created: Math.floor(Date.now() / 1000),
                      model: modelId,
                      choices: [
                        {
                          index: 0,
                          delta: {
                            content: part.text,
                          },
                          finishReason: null,
                        },
                      ],
                    };
                  } else if (part.functionCall) {
                    // Handle function call streaming
                    const functionCall = part.functionCall;
                    
                    yield {
                      id: messageId,
                      object: 'chat.completion.chunk',
                      created: Math.floor(Date.now() / 1000),
                      model: modelId,
                      choices: [
                        {
                          index: 0,
                          delta: {
                            functionCall: {
                              name: functionCall.name,
                              arguments: JSON.stringify(functionCall.args),
                            },
                          },
                          finishReason: null,
                        },
                      ],
                    };
                  }
                }
              }
              
              if (candidate.finishReason) {
                yield {
                  id: messageId,
                  object: 'chat.completion.chunk',
                  created: Math.floor(Date.now() / 1000),
                  model: modelId,
                  choices: [
                    {
                      index: 0,
                      delta: {},
                      finishReason: candidate.finishReason.toLowerCase(),
                    },
                  ],
                };
              }
            }
          } catch (e) {
            // Ignore parsing errors for incomplete JSON
          }
        }
      }

      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += totalTokens;
      this.metrics.totalCost += this.estimateCost(totalTokens, modelId);
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  async createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      const modelId = request.model || 'embedding-001';
      const inputs = Array.isArray(request.input) ? request.input : [request.input];
      
      const embeddings: number[][] = [];
      let totalTokens = 0;
      
      // Process each input text
      for (const input of inputs) {
        const url = `/${this.apiVersion}/models/${modelId}:embedContent?key=${this.apiKey}`;
        const response = await this.client.post(url, {
          content: { parts: [{ text: input }] },
          taskType: 'RETRIEVAL_DOCUMENT',
          title: request.metadata?.title,
        });
        
        const data = response.data;
        const embedding = data.embedding.values;
        embeddings.push(embedding);
        
        // Estimate tokens
        totalTokens += this.estimateTokens(input);
      }
      
      this.metrics.successfulRequests++;
      this.metrics.totalTokensUsed += totalTokens;
      this.metrics.totalCost += this.estimateCost(totalTokens, modelId);
      
      const latency = Date.now() - startTime;
      this.updateAverageLatency(latency);

      return {
        object: 'list',
        data: embeddings.map((embedding, index) => ({
          object: 'embedding',
          embedding,
          index,
        })),
        model: modelId,
        usage: {
          promptTokens: totalTokens,
          totalTokens: totalTokens,
        },
      };
    } catch (error: unknown) {
      this.metrics.failedRequests++;
      throw this.handleError(error);
    }
  }

  validateConfig(config: ModelConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.model || !this.supportedModels.includes(config.model)) {
      errors.push(`Unsupported model: ${config.model}`);
    }

    if (config.temperature < 0 || config.temperature > 1) {
      errors.push('Temperature must be between 0 and 1');
    }

    if (config.maxTokens < 1 || config.maxTokens > 8192) {
      errors.push('Max tokens must be between 1 and 8192');
    }

    if (config.topP && (config.topP < 0 || config.topP > 1)) {
      errors.push('Top P must be between 0 and 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  estimateCost(tokens: number, model: string): number {
    // Gemini pricing (per 1M tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'gemini-1.5-pro': { input: 7, output: 21 },
      'gemini-1.5-flash': { input: 0.35, output: 1.05 },
      'gemini-1.0-pro': { input: 0.125, output: 0.375 },
      'embedding-001': { input: 0.25, output: 0 },
    };

    const modelPricing = pricing[model] || pricing[this.defaultModel];
    // Assuming 70% input, 30% output ratio
    const inputTokens = tokens * 0.7;
    const outputTokens = tokens * 0.3;
    
    // Convert from per 1M tokens to per token
    return ((inputTokens * modelPricing.input) + (outputTokens * modelPricing.output)) / 1000000;
  }

  formatMessages(messages: ChatMessage[]): GeminiContent[] {
    const formattedMessages: GeminiContent[] = [];
    let currentRole: string | null = null;
    let currentParts: GeminiPart[] = [];
    
    for (const msg of messages) {
      // Handle system messages as user messages with a special prefix
      if (msg.role === 'system') {
        formattedMessages.push({
          role: 'user',
          parts: [{ text: `System: ${msg.content}` }],
        });
        continue;
      }
      
      // Handle function messages
      if (msg.role === 'function') {
        formattedMessages.push({
          role: 'function',
          parts: [
            {
              functionResponse: {
                name: msg.name || 'unknown_function',
                response: { result: msg.content },
              },
            },
          ],
        });
        continue;
      }
      
      // Map to Gemini roles
      const geminiRole = msg.role === 'assistant' ? 'model' : 'user';
      
      // Create parts based on content
      const parts: GeminiPart[] = [];
      
      // Handle text content
      if (typeof msg.content === 'string') {
        parts.push({ text: msg.content });
      }
      
      // Handle function calls
      if (msg.functionCall) {
        parts.push({
          functionCall: {
            name: msg.functionCall.name,
            args: JSON.parse(msg.functionCall.arguments),
          },
        });
      }
      
      // Handle tool calls
      if (msg.toolCalls && msg.toolCalls.length > 0) {
        for (const toolCall of msg.toolCalls) {
          if (toolCall.type === 'function') {
            parts.push({
              functionCall: {
                name: toolCall.function.name,
                args: JSON.parse(toolCall.function.arguments),
              },
            });
          }
        }
      }
      
      formattedMessages.push({
        role: geminiRole,
        parts,
      });
    }
    
    return formattedMessages;
  }

  parseResponse(response: any): ChatCompletionResponse {
    // Extract the model ID from the response (added in chatCompletion method)
    const modelId = response._modelId || this.defaultModel;
    const id = `gemini-${Date.now()}`;
    const created = Math.floor(Date.now() / 1000);
    
    if (!response.candidates || response.candidates.length === 0) {
      return {
        id,
        object: 'chat.completion',
        created,
        model: modelId,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: '',
            },
            finishReason: 'error',
          },
        ],
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0,
        },
      };
    }
    
    const candidate = response.candidates[0];
    const content = this.extractContentFromCandidate(candidate);
    const functionCall = this.extractFunctionCallFromCandidate(candidate);
    
    return {
      id,
      object: 'chat.completion',
      created,
      model: modelId,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: content,
            functionCall: functionCall,
          },
          finishReason: candidate.finishReason?.toLowerCase() || 'stop',
        },
      ],
      usage: {
        promptTokens: 0, // Will be filled in by the calling method
        completionTokens: 0,
        totalTokens: 0,
      },
    };
  }

  handleError(error: unknown): ProviderError {
    const err = error as any;
    let errorType: ProviderError['type'] = 'server_error';
    let retryable = false;
    let retryAfter: number | undefined;

    if (err.response) {
      const status = err.response.status;
      
      if (status === 429) {
        errorType = 'rate_limit';
        retryable = true;
        retryAfter = 60; // Default retry after 60 seconds
      } else if (status === 413 || status === 400) {
        errorType = 'context_length';
        retryable = false;
      } else if (status >= 400 && status < 500) {
        errorType = 'invalid_request';
        retryable = false;
      }
    } else if (err.code === 'ECONNABORTED' || err.code === 'ETIMEDOUT') {
      errorType = 'timeout';
      retryable = true;
    }

    return {
      code: err.code || 'UNKNOWN',
      message: err.message || 'An unknown error occurred',
      type: errorType,
      retryable,
      retryAfter,
      details: err,
    };
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      totalTokensUsed: 0,
      totalCost: 0,
      rateLimit: {
        limit: 60, // Default Gemini rate limit (requests per minute)
        remaining: 60,
        resetTime: new Date(Date.now() + 60000),
      },
    };
  }

  private calculateErrorRate(): number {
    const total = this.metrics.totalRequests;
    return total > 0 ? (this.metrics.failedRequests / total) * 100 : 0;
  }

  private updateAverageLatency(latency: number): void {
    const total = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageLatency = 
      (this.metrics.averageLatency * (total - 1) + latency) / total;
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for Gemini
    return Math.ceil(text.length / 4);
  }

  private estimatePromptTokens(messages: ChatMessage[]): number {
    let totalTokens = 0;
    
    for (const msg of messages) {
      if (typeof msg.content === 'string') {
        totalTokens += this.estimateTokens(msg.content);
      }
      
      if (msg.functionCall) {
        totalTokens += this.estimateTokens(msg.functionCall.name + msg.functionCall.arguments);
      }
      
      if (msg.toolCalls) {
        for (const toolCall of msg.toolCalls) {
          if (toolCall.function) {
            totalTokens += this.estimateTokens(toolCall.function.name + toolCall.function.arguments);
          }
        }
      }
    }
    
    return totalTokens;
  }

  private estimateCompletionTokens(text: string): number {
    return this.estimateTokens(text);
  }

  private formatFunctions(functions: any[]): any[] {
    return functions.map(fn => {
      // Handle OpenAI-style function definitions
      if (fn.function) {
        return {
          name: fn.function.name,
          description: fn.function.description || '',
          parameters: fn.function.parameters || {},
        };
      }
      
      // Handle direct function definitions
      return {
        name: fn.name,
        description: fn.description || '',
        parameters: fn.parameters || {},
      };
    });
  }

  private extractContentFromCandidate(candidate: any): string {
    let content = '';
    
    if (candidate.content && candidate.content.parts) {
      for (const part of candidate.content.parts) {
        if (part.text) {
          content += part.text;
        }
      }
    }
    
    return content;
  }

  private extractFunctionCallFromCandidate(candidate: any): any {
    if (candidate.content && candidate.content.parts) {
      for (const part of candidate.content.parts) {
        if (part.functionCall) {
          return {
            name: part.functionCall.name,
            arguments: JSON.stringify(part.functionCall.args),
          };
        }
      }
    }
    
    return undefined;
  }
}
