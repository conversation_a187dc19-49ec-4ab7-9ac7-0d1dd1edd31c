import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { ProvidersService } from './providers.service';
import { getErrorMessage } from '@synapseai/utils';

@Injectable()
export class ProviderHealthService {
  private readonly logger = new Logger(ProviderHealthService.name);

  constructor(private readonly providersService: ProvidersService) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async checkProvidersHealth(): Promise<void> {
    try {
      const health = await this.providersService.getAllProvidersHealth();
      
      for (const [providerName, status] of Object.entries(health)) {
        if (status.status === 'unhealthy') {
          this.logger.warn(`Provider ${providerName} is unhealthy: ${status.error || 'Unknown error'}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to check providers health: ${getErrorMessage(error)}`);
    }
  }

  async getHealthStatus(): Promise<Record<string, any>> {
    return this.providersService.getAllProvidersHealth();
  }

  async getDetailedHealthReport(): Promise<any> {
    const health = await this.getHealthStatus();
    
    return {
      timestamp: new Date().toISOString(),
      providers: health,
      summary: {
        total: Object.keys(health).length,
        healthy: Object.values(health).filter((status: any) => status.status === 'healthy').length,
        unhealthy: Object.values(health).filter((status: any) => status.status === 'unhealthy').length,
        degraded: Object.values(health).filter((status: any) => status.status === 'degraded').length,
      },
    };
  }
}