import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';

interface MetricsData {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  totalTokensUsed: number;
  totalCost: number;
}

@Injectable()
export class ProviderMetricsService {
  private readonly logger = new Logger(ProviderMetricsService.name);

  constructor(private readonly redisService: RedisService) {}

  async recordMetrics(
    providerName: string,
    metrics: {
      requestCount: number;
      success: boolean;
      latency: number;
      tokensUsed: number;
      cost: number;
    },
  ): Promise<void> {
    const metricsKey = `metrics:provider:${providerName}`;
    
    await Promise.all([
      this.redisService.hincr(metricsKey, 'totalRequests', metrics.requestCount),
      this.redisService.hincr(metricsKey, metrics.success ? 'successfulRequests' : 'failedRequests', metrics.requestCount),
      this.redisService.hincr(metricsKey, 'totalTokensUsed', metrics.tokensUsed),
      this.redisService.hset(metricsKey, 'totalCost', (await this.getTotalCost(providerName)) + metrics.cost),
    ]);

    // Update average latency
    const currentLatency = await this.getAverageLatency(providerName);
    const totalRequests = await this.getTotalRequests(providerName);
    const newAverage = (currentLatency * (totalRequests - 1) + metrics.latency) / totalRequests;
    
    await this.redisService.hset(metricsKey, 'averageLatency', newAverage);
  }

  async getMetrics(providerName: string): Promise<MetricsData> {
    const metricsKey = `metrics:provider:${providerName}`;
    const data = await this.redisService.hgetall(metricsKey);
    
    return {
      totalRequests: parseInt(data.totalRequests || '0', 10),
      successfulRequests: parseInt(data.successfulRequests || '0', 10),
      failedRequests: parseInt(data.failedRequests || '0', 10),
      averageLatency: parseFloat(data.averageLatency || '0'),
      totalTokensUsed: parseInt(data.totalTokensUsed || '0', 10),
      totalCost: parseFloat(data.totalCost || '0'),
    };
  }

  async getAllMetrics(): Promise<Record<string, MetricsData>> {
    const pattern = 'metrics:provider:*';
    const keys = await this.redisService.keys(pattern);
    const metrics: Record<string, MetricsData> = {};
    
    for (const key of keys) {
      const providerName = key.replace('metrics:provider:', '');
      metrics[providerName] = await this.getMetrics(providerName);
    }
    
    return metrics;
  }

  async resetMetrics(providerName: string): Promise<void> {
    const metricsKey = `metrics:provider:${providerName}`;
    await this.redisService.del(metricsKey);
  }

  private async getTotalCost(providerName: string): Promise<number> {
    const cost = await this.redisService.hget(`metrics:provider:${providerName}`, 'totalCost');
    return cost ? parseFloat(cost) : 0;
  }

  private async getAverageLatency(providerName: string): Promise<number> {
    const latency = await this.redisService.hget(`metrics:provider:${providerName}`, 'averageLatency');
    return latency ? parseFloat(latency) : 0;
  }

  private async getTotalRequests(providerName: string): Promise<number> {
    const requests = await this.redisService.hget(`metrics:provider:${providerName}`, 'totalRequests');
    return requests ? parseInt(requests, 10) : 0;
  }
}