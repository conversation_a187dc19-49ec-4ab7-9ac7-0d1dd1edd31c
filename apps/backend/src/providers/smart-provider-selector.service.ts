import { Injectable, Logger } from '@nestjs/common';

import { ProvidersService } from './providers.service';
import { BaseProvider } from './interfaces/provider.interface';

interface ProviderSelection {
  provider: BaseProvider;
  reason: string;
  confidence: number;
}

interface ProviderScore {
  name: string;
  provider: BaseProvider;
  score: number;
  reason: string;
}

@Injectable()
export class SmartProviderSelector {
  private readonly logger = new Logger(SmartProviderSelector.name);
  private performanceCache: Record<string, {
    responseTime: number[];
    tokenUsage: number[];
    cost: number[];
    quality: number[];
    lastUpdated: Date;
  }> = {};

  constructor(private readonly providersService: ProvidersService) {}

  async selectProvider(
    requirements: {
      model?: string;
      streaming?: boolean;
      functionCalling?: boolean;
      multimodal?: boolean;
      maxTokens?: number;
      priority?: 'cost' | 'speed' | 'quality';
      task?: 'chat' | 'code' | 'reasoning' | 'math' | 'writing' | 'summarization' | 'extraction' | 'classification';
    },
  ): Promise<ProviderSelection> {
    const availableProviders = this.providersService.getAvailableProviders();
    
    if (availableProviders.length === 0) {
      throw new Error('No providers available');
    }

    // If a specific model is requested, find the provider that supports it
    if (requirements.model) {
      for (const providerName of availableProviders) {
        const provider = await this.providersService.getProvider(providerName);
        
        // Check if the model name includes the provider name (e.g., openai/gpt-4)
        if (requirements.model.includes('/')) {
          const [providerPrefix] = requirements.model.split('/');
          if (providerName === providerPrefix.toLowerCase()) {
            return {
              provider,
              reason: `Selected provider ${providerName} for requested model ${requirements.model}`,
              confidence: 1.0,
            };
          }
        }
        
        // Check if the provider directly supports the model
        if (provider.supportedModels.includes(requirements.model)) {
          return {
            provider,
            reason: `Selected provider ${providerName} for requested model ${requirements.model}`,
            confidence: 1.0,
          };
        }
      }
    }

    // Score each provider based on requirements
    const providerScores: ProviderScore[] = [];
    
    for (const providerName of availableProviders) {
      const provider = await this.providersService.getProvider(providerName);
      let score = 0;
      let reason = '';
      
      // Check capabilities
      if (requirements.streaming && provider.capabilities.streaming) {
        score += 1;
      } else if (requirements.streaming) {
        score -= 10; // Strongly penalize if streaming is required but not supported
      }
      
      if (requirements.functionCalling && provider.capabilities.functionCalling) {
        score += 1;
      } else if (requirements.functionCalling) {
        score -= 10; // Strongly penalize if function calling is required but not supported
      }
      
      if (requirements.multimodal && provider.capabilities.multimodal) {
        score += 1;
      } else if (requirements.multimodal) {
        score -= 10; // Strongly penalize if multimodal is required but not supported
      }
      
      if (requirements.maxTokens && provider.capabilities.maxContextLength >= requirements.maxTokens) {
        score += 1;
      } else if (requirements.maxTokens && provider.capabilities.maxContextLength < requirements.maxTokens) {
        score -= 5; // Penalize if context length is insufficient
      }
      
      // Prioritize based on user preference
      if (requirements.priority) {
        switch (requirements.priority) {
          case 'cost':
            // Prefer cheaper providers
            if (providerName === 'groq' || providerName === 'mistral') {
              score += 3;
              reason = 'Selected for cost efficiency';
            } else if (providerName === 'openai') {
              score += 1;
            }
            break;
          case 'speed':
            // Prefer faster providers
            if (providerName === 'groq') {
              score += 5;
              reason = 'Selected for high-performance inference';
            } else if (providerName === 'openai') {
              score += 3;
              reason = 'Selected for good balance of speed and quality';
            }
            break;
          case 'quality':
            // Prefer higher quality providers
            if (providerName === 'openai' || providerName === 'claude') {
              score += 3;
              reason = 'Selected for high-quality responses';
            } else if (providerName === 'mistral' && requirements.task === 'code') {
              score += 4;
              reason = 'Selected for specialized code generation';
            }
            break;
        }
      }
      
      // Task-specific provider selection
      if (requirements.task) {
        switch (requirements.task) {
          case 'code':
            if (providerName === 'mistral' || providerName === 'openai') {
              score += 3;
              reason = reason || 'Selected for code generation capabilities';
            }
            break;
          case 'reasoning':
            if (providerName === 'claude' || providerName === 'openai') {
              score += 3;
              reason = reason || 'Selected for reasoning capabilities';
            }
            break;
          case 'math':
            if (providerName === 'mistral' || providerName === 'openai') {
              score += 3;
              reason = reason || 'Selected for mathematical problem-solving';
            }
            break;
          case 'writing':
            if (providerName === 'claude' || providerName === 'openai') {
              score += 3;
              reason = reason || 'Selected for writing capabilities';
            }
            break;
          case 'summarization':
            if (providerName === 'mistral' || providerName === 'claude') {
              score += 3;
              reason = reason || 'Selected for summarization capabilities';
            }
            break;
          case 'extraction':
            if (providerName === 'mistral' || providerName === 'openai') {
              score += 3;
              reason = reason || 'Selected for information extraction capabilities';
            }
            break;
          case 'classification':
            if (providerName === 'mistral' || providerName === 'openai') {
              score += 3;
              reason = reason || 'Selected for classification capabilities';
            }
            break;
        }
      }
      
      // Consider past performance if available
      if (this.performanceCache[providerName]) {
        const performance = this.performanceCache[providerName];
        const avgResponseTime = performance.responseTime.reduce((a, b) => a + b, 0) / performance.responseTime.length;
        const avgQuality = performance.quality.reduce((a, b) => a + b, 0) / performance.quality.length;
        
        if (requirements.priority === 'speed' && avgResponseTime < 2000) {
          score += 2;
        }
        
        if (requirements.priority === 'quality' && avgQuality > 0.8) {
          score += 2;
        }
      }
      
      // Add to scores
      providerScores.push({
        name: providerName,
        provider,
        score,
        reason: reason || 'Selected based on requirements',
      });
    }
    
    // Sort by score and select the best provider
    providerScores.sort((a, b) => b.score - a.score);
    
    const bestProvider = providerScores[0];
    
    // If all providers have negative scores, default to OpenAI
    if (bestProvider.score < 0 && availableProviders.includes('openai')) {
      const openaiProvider = await this.providersService.getProvider('openai');
      return {
        provider: openaiProvider,
        reason: 'Defaulting to OpenAI as no provider fully meets requirements',
        confidence: 0.5,
      };
    }
    
    // Calculate confidence based on score difference with the second-best provider
    let confidence = 0.8; // Default confidence
    if (providerScores.length > 1) {
      const scoreDiff = bestProvider.score - providerScores[1].score;
      confidence = Math.min(1.0, 0.7 + (scoreDiff / 10));
    }
    
    return {
      provider: bestProvider.provider,
      reason: bestProvider.reason,
      confidence,
    };
  }

  async selectOptimalProvider(
    context: {
      messageLength: number;
      conversationHistory: number;
      userPreferences?: Record<string, any>;
      budget?: number;
      task?: 'chat' | 'code' | 'reasoning' | 'math' | 'writing' | 'summarization' | 'extraction' | 'classification';
    },
  ): Promise<ProviderSelection> {
    // Determine requirements based on context
    const requirements: any = {};
    
    // Set max tokens based on conversation history
    requirements.maxTokens = context.messageLength + context.conversationHistory;
    
    // Set priority based on user preferences or budget
    if (context.budget && context.budget < 0.01) {
      requirements.priority = 'cost';
    } else if (context.userPreferences?.priority) {
      requirements.priority = context.userPreferences.priority;
    } else {
      requirements.priority = 'quality';
    }
    
    // Set task if specified
    if (context.task) {
      requirements.task = context.task;
    }
    
    // Set multimodal if needed
    if (context.userPreferences?.multimodal) {
      requirements.multimodal = true;
    }
    
    return this.selectProvider(requirements);
  }

  async evaluateProviderPerformance(
    providerName: string,
    metrics: {
      responseTime: number;
      tokenUsage: number;
      cost: number;
      quality: number;
    },
  ): Promise<void> {
    // Initialize cache entry if it doesn't exist
    if (!this.performanceCache[providerName]) {
      this.performanceCache[providerName] = {
        responseTime: [],
        tokenUsage: [],
        cost: [],
        quality: [],
        lastUpdated: new Date(),
      };
    }
    
    // Add new metrics
    this.performanceCache[providerName].responseTime.push(metrics.responseTime);
    this.performanceCache[providerName].tokenUsage.push(metrics.tokenUsage);
    this.performanceCache[providerName].cost.push(metrics.cost);
    this.performanceCache[providerName].quality.push(metrics.quality);
    this.performanceCache[providerName].lastUpdated = new Date();
    
    // Limit cache size to last 100 entries
    if (this.performanceCache[providerName].responseTime.length > 100) {
      this.performanceCache[providerName].responseTime.shift();
      this.performanceCache[providerName].tokenUsage.shift();
      this.performanceCache[providerName].cost.shift();
      this.performanceCache[providerName].quality.shift();
    }
    
    this.logger.debug(`Updated performance metrics for provider ${providerName}`, metrics);
  }
}
