import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { BaseProvider } from './interfaces/provider.interface';
import { ClaudeProvider } from './providers/claude.provider';
import { GeminiProvider } from './providers/gemini.provider';
import { GroqProvider } from './providers/groq.provider';
import { MistralProvider } from './providers/mistral.provider';
import { OpenAIProvider } from './providers/openai.provider';
import { OpenRouterProvider } from './providers/openrouter.provider';

@Injectable()
export class ProvidersService {
  private readonly logger = new Logger(ProvidersService.name);
  private readonly providers = new Map<string, BaseProvider>();

  constructor(
    private readonly configService: ConfigService,
    private readonly openaiProvider: OpenAIProvider,
    private readonly claudeProvider: ClaudeProvider,
    private readonly geminiProvider: GeminiProvider,
    private readonly mistralProvider: MistralProvider,
    private readonly groqProvider: GroqProvider,
    private readonly openRouterProvider: OpenRouterProvider,
  ) {
    this.initializeProviders();
  }

  private async initializeProviders(): Promise<void> {
    const providers = [
      this.openaiProvider,
      this.claudeProvider,
      this.geminiProvider,
      this.mistralProvider,
      this.groqProvider,
      this.openRouterProvider,
    ];

    for (const provider of providers) {
      try {
        await provider.initialize({});
        this.providers.set(provider.name, provider);
        this.logger.log(`Initialized provider: ${provider.name}`);
      } catch (error: unknown) {
        this.logger.error(`Failed to initialize provider ${provider.name}: ${(error as Error).message || 'Unknown error'}`);
      }
    }
  }

  async getProvider(name: string): Promise<BaseProvider> {
    const provider = this.providers.get(name);
    if (!provider) {
      throw new NotFoundException(`Provider ${name} not found`);
    }
    return provider;
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  async getProviderHealth(name: string): Promise<any> {
    const provider = await this.getProvider(name);
    return provider.healthCheck();
  }

  async getProviderMetrics(name: string): Promise<any> {
    const provider = await this.getProvider(name);
    return provider.getMetrics();
  }

  async getAllProvidersHealth(): Promise<Record<string, any>> {
    const health: Record<string, any> = {};
    
    for (const [name, provider] of this.providers.entries()) {
      try {
        health[name] = await provider.healthCheck();
      } catch (error: unknown) {
        health[name] = {
          status: 'unhealthy',
          error: (error as Error).message || 'Unknown error',
        };
      }
    }
    
    return health;
  }
}
