import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { CommonModule } from '../common/common.module';

import { ProviderHealthService } from './provider-health.service';
import { ProviderMetricsService } from './provider-metrics.service';
import { ProvidersController } from './providers.controller';
import { ProvidersService } from './providers.service';
import { ClaudeProvider } from './providers/claude.provider';
import { GeminiProvider } from './providers/gemini.provider';
import { GroqProvider } from './providers/groq.provider';
import { MistralProvider } from './providers/mistral.provider';
import { OpenAIProvider } from './providers/openai.provider';
import { OpenRouterProvider } from './providers/openrouter.provider';
import { SmartProviderSelector } from './smart-provider-selector.service';

@Module({
  imports: [ConfigModule, CommonModule],
  controllers: [ProvidersController],
  providers: [
    ProvidersService,
    SmartProviderSelector,
    OpenAIProvider,
    ClaudeProvider,
    GeminiProvider,
    MistralProvider,
    GroqProvider,
    OpenRouterProvider,
    ProviderHealthService,
    ProviderMetricsService,
  ],
  exports: [
    ProvidersService,
    SmartProviderSelector,
    OpenAIProvider,
    ClaudeProvider,
    GeminiProvider,
    MistralProvider,
    GroqProvider,
    OpenRouterProvider,
  ],
})
export class ProvidersModule {}
