export interface ModelConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  maxRetries?: number;
  timeout?: number;
}

export interface ProviderCapabilities {
  streaming: boolean;
  functionCalling: boolean;
  multimodal: boolean;
  embedding: boolean;
  imageGeneration: boolean;
  codeExecution: boolean;
  webSearch: boolean;
  maxContextLength: number;
  supportedFormats: string[];
}

export interface ProviderHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency: number;
  uptime: number;
  errorRate: number;
  lastChecked: Date;
  issues: string[];
}

export interface ProviderMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  totalTokensUsed: number;
  totalCost: number;
  rateLimit: {
    limit: number;
    remaining: number;
    resetTime: Date;
  };
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string;
  functionCall?: {
    name: string;
    arguments: string;
  };
  toolCalls?: {
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }[];
}

export interface ChatCompletionRequest {
  messages: ChatMessage[];
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  stream?: boolean;
  functions?: any[];
  tools?: any[];
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: ChatMessage;
    finishReason: string;
    delta?: Partial<ChatMessage>;
  }[];
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  metadata?: Record<string, any>;
}

export interface StreamingResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    delta: Partial<ChatMessage>;
    finishReason: string | null;
  }[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface EmbeddingRequest {
  input: string | string[];
  model: string;
  dimensions?: number;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface EmbeddingResponse {
  object: string;
  data: {
    object: string;
    embedding: number[];
    index: number;
  }[];
  model: string;
  usage: {
    promptTokens: number;
    totalTokens: number;
  };
}

export interface ProviderError {
  code: string;
  message: string;
  type: 'rate_limit' | 'context_length' | 'invalid_request' | 'server_error' | 'timeout';
  retryable: boolean;
  retryAfter?: number;
  details?: any;
}

export abstract class BaseProvider {
  abstract readonly name: string;
  abstract readonly capabilities: ProviderCapabilities;
  abstract readonly supportedModels: string[];
  abstract readonly defaultModel: string;

  abstract initialize(config: Record<string, any>): Promise<void>;
  abstract healthCheck(): Promise<ProviderHealth>;
  abstract getMetrics(): Promise<ProviderMetrics>;
  abstract chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  abstract streamChatCompletion(request: ChatCompletionRequest): AsyncIterable<StreamingResponse>;
  abstract createEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse>;
  abstract validateConfig(config: ModelConfig): { isValid: boolean; errors: string[] };
  abstract estimateCost(tokens: number, model: string): number;
  abstract formatMessages(messages: ChatMessage[]): any[];
  abstract parseResponse(response: any): ChatCompletionResponse;
  abstract handleError(error: any): ProviderError;
}