import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { TenantGuard } from '../auth/guards/tenant.guard';
import { Roles } from '../auth/decorators/roles.decorator';

import { ProviderHealthService } from './provider-health.service';
import { ProviderMetricsService } from './provider-metrics.service';
import { ProvidersService } from './providers.service';

@ApiTags('Providers')
@ApiBearerAuth()
@Controller('providers')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
export class ProvidersController {
  constructor(
    private readonly providersService: ProvidersService,
    private readonly healthService: ProviderHealthService,
    private readonly metricsService: ProviderMetricsService,
  ) {}

  @Get()
  @Roles('admin', 'developer', 'viewer')
  @ApiOperation({ summary: 'Get all available providers' })
  @ApiResponse({ status: 200, description: 'Providers retrieved successfully' })
  async getProviders(): Promise<string[]> {
    return this.providersService.getAvailableProviders();
  }

  @Get('health')
  @Roles('admin', 'developer', 'viewer')
  @ApiOperation({ summary: 'Get providers health status' })
  @ApiResponse({ status: 200, description: 'Health status retrieved successfully' })
  async getHealthStatus(): Promise<Record<string, any>> {
    return this.healthService.getHealthStatus();
  }

  @Get('health/detailed')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Get detailed health report' })
  @ApiResponse({ status: 200, description: 'Detailed health report retrieved successfully' })
  async getDetailedHealthReport(): Promise<any> {
    return this.healthService.getDetailedHealthReport();
  }

  @Get('metrics')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Get providers metrics' })
  @ApiResponse({ status: 200, description: 'Metrics retrieved successfully' })
  async getMetrics(): Promise<Record<string, any>> {
    return this.metricsService.getAllMetrics();
  }

  @Get(':name/health')
  @Roles('admin', 'developer', 'viewer')
  @ApiOperation({ summary: 'Get specific provider health' })
  @ApiResponse({ status: 200, description: 'Provider health retrieved successfully' })
  async getProviderHealth(@Param('name') name: string): Promise<any> {
    return this.providersService.getProviderHealth(name);
  }

  @Get(':name/metrics')
  @Roles('admin', 'developer')
  @ApiOperation({ summary: 'Get specific provider metrics' })
  @ApiResponse({ status: 200, description: 'Provider metrics retrieved successfully' })
  async getProviderMetrics(@Param('name') name: string): Promise<any> {
    return this.metricsService.getMetrics(name);
  }
}